use crate::commands::plagiarism::{Plagiarism<PERSON>atch, <PERSON>tenceMatch, BatchStatistics};
use crate::database::plagiarism_batch::PlagiarismBatchRepository;
use crate::database::sentence_match::SentenceMatchRepository;
use crate::models::plagiarism::{PlagiarismBatchDb, SentenceMatchDb};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::sync::RwLock;
use chrono::Utc;

#[derive(Clone)]
pub struct BatchManager {
    batches: Arc<RwLock<HashMap<String, PlagiarismBatch>>>,
    matches: Arc<RwLock<HashMap<String, Vec<SentenceMatch>>>>,
    running_tasks: Arc<Mutex<HashMap<String, bool>>>,
}

impl BatchManager {
    pub fn new() -> Self {
        Self {
            batches: Arc::new(RwLock::new(HashMap::new())),
            matches: Arc::new(RwLock::new(HashMap::new())),
            running_tasks: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 创建新的查重批次
    pub async fn create_batch(
        &self,
        name: String,
        description: Option<String>,
        book_ids: Vec<String>,
    ) -> String {
        // 创建数据库批次记录
        let mut db_batch = PlagiarismBatchDb::new(name, description, book_ids);

        // 保存到数据库
        if let Err(e) = PlagiarismBatchRepository::save(&mut db_batch).await {
            eprintln!("保存批次到数据库失败: {}", e);
            return self.generate_batch_id(); // 返回一个临时ID
        }

        let batch_id = db_batch.batch_id.to_string();

        // 同时保存到内存缓存（用于运行时状态管理）
        let batch: PlagiarismBatch = db_batch.into();
        let mut batches = self.batches.write().await;
        batches.insert(batch_id.clone(), batch);

        batch_id
    }

    /// 获取批次信息
    pub async fn get_batch(&self, batch_id: &str) -> Option<PlagiarismBatch> {
        // 首先尝试从内存缓存获取
        {
            let batches = self.batches.read().await;
            if let Some(batch) = batches.get(batch_id) {
                return Some(batch.clone());
            }
        }

        // 如果内存中没有，从数据库获取
        match PlagiarismBatchRepository::find_by_batch_id_str(batch_id.to_string()).await {
            Ok(Some(db_batch)) => {
                let batch: PlagiarismBatch = db_batch.into();

                // 更新内存缓存
                let mut batches = self.batches.write().await;
                batches.insert(batch_id.to_string(), batch.clone());

                Some(batch)
            }
            Ok(None) => None,
            Err(e) => {
                eprintln!("从数据库获取批次失败: {}", e);
                None
            }
        }
    }

    /// 更新批次状态
    pub async fn update_batch_status(&self, batch_id: &str, status: &str, progress: Option<i32>) {
        // 解析批次ID
        if let Ok(batch_id_u64) = batch_id.parse::<u64>() {
            // 更新数据库
            if let Err(e) = PlagiarismBatchRepository::update_status(batch_id_u64, status.to_string(), progress).await {
                eprintln!("更新数据库批次状态失败: {}", e);
            }
        }

        // 更新内存缓存
        let mut batches = self.batches.write().await;
        if let Some(batch) = batches.get_mut(batch_id) {
            batch.status = status.to_string();
            batch.update_time = Utc::now().to_rfc3339();
            if let Some(p) = progress {
                batch.progress = Some(p);
            }
            if status == "completed" || status == "failed" {
                batch.completed_time = Some(Utc::now().to_rfc3339());
            }
        }
    }

    /// 更新批次匹配数量
    pub async fn update_batch_matches(&self, batch_id: &str, total_matches: i32) {
        // 解析批次ID
        if let Ok(batch_id_u64) = batch_id.parse::<u64>() {
            // 更新数据库
            if let Err(e) = PlagiarismBatchRepository::update_total_matches(batch_id_u64, total_matches).await {
                eprintln!("更新数据库批次匹配数量失败: {}", e);
            }
        }

        // 更新内存缓存
        let mut batches = self.batches.write().await;
        if let Some(batch) = batches.get_mut(batch_id) {
            batch.total_matches = total_matches;
            batch.update_time = Utc::now().to_rfc3339();
        }
    }

    /// 添加匹配结果
    pub async fn add_matches(&self, batch_id: &str, new_matches: Vec<SentenceMatch>) {
        if new_matches.is_empty() {
            return;
        }

        // 解析批次ID
        let batch_id_u64 = match batch_id.parse::<u64>() {
            Ok(id) => id,
            Err(_) => {
                eprintln!("无效的批次ID: {}", batch_id);
                return;
            }
        };

        // 转换为数据库模型并保存到数据库
        let db_matches: Vec<SentenceMatchDb> = new_matches.iter().map(|m| {
            SentenceMatchDb::new(
                batch_id_u64,
                m.source_book_id.clone(),
                m.source_book_name.clone(),
                m.source_page,
                m.source_card_id,
                m.source_content.clone(),
                m.target_book_id.clone(),
                m.target_book_name.clone(),
                m.target_page,
                m.target_card_id,
                m.target_content.clone(),
                m.similarity,
                m.match_type.clone(),
            )
        }).collect();

        // 批量保存到数据库
        if let Err(e) = SentenceMatchRepository::save_batch(&db_matches).await {
            eprintln!("保存匹配结果到数据库失败: {}", e);
        }

        // 同时保存到内存缓存
        let mut matches = self.matches.write().await;
        let batch_matches = matches.entry(batch_id.to_string()).or_insert_with(Vec::new);
        batch_matches.extend(new_matches);

        // 更新批次的匹配数量
        self.update_batch_matches(batch_id, batch_matches.len() as i32).await;
    }

    /// 获取批次的匹配结果
    pub async fn get_matches(&self, batch_id: &str, page_no: i32, page_size: i32) -> (Vec<SentenceMatch>, i32) {
        // 解析批次ID
        let batch_id_u64 = match batch_id.parse::<u64>() {
            Ok(id) => id,
            Err(_) => {
                eprintln!("无效的批次ID: {}", batch_id);
                return (Vec::new(), 0);
            }
        };

        // 从数据库获取分页结果
        match SentenceMatchRepository::find_by_batch_id_paginated(
            batch_id_u64,
            page_no,
            page_size,
            None, None, None, None, None
        ).await {
            Ok((db_matches, total)) => {
                let matches: Vec<SentenceMatch> = db_matches.into_iter().map(|m| m.into()).collect();
                (matches, total)
            }
            Err(e) => {
                eprintln!("从数据库获取匹配结果失败: {}", e);

                // 回退到内存缓存
                let matches = self.matches.read().await;
                if let Some(batch_matches) = matches.get(batch_id) {
                    let total = batch_matches.len() as i32;
                    let start = (page_no * page_size) as usize;
                    let end = ((page_no + 1) * page_size) as usize;

                    let page_matches = if start < batch_matches.len() {
                        batch_matches[start..end.min(batch_matches.len())].to_vec()
                    } else {
                        Vec::new()
                    };

                    (page_matches, total)
                } else {
                    (Vec::new(), 0)
                }
            }
        }
    }

    /// 获取批次列表
    pub async fn get_batch_list(
        &self,
        page_no: i32,
        page_size: i32,
        status_filter: Option<&str>,
        keyword: Option<&str>,
    ) -> (Vec<PlagiarismBatch>, i32) {
        // 从数据库获取分页结果
        match PlagiarismBatchRepository::find_batches_paginated(
            page_no,
            page_size,
            status_filter.map(|s| s.to_string()),
            keyword.map(|s| s.to_string()),
        ).await {
            Ok((db_batches, total)) => {
                let batches: Vec<PlagiarismBatch> = db_batches.into_iter().map(|b| b.into()).collect();
                (batches, total)
            }
            Err(e) => {
                eprintln!("从数据库获取批次列表失败: {}", e);

                // 回退到内存缓存
                let batches = self.batches.read().await;
                let mut filtered_batches: Vec<PlagiarismBatch> = batches
                    .values()
                    .filter(|batch| {
                        // 状态筛选
                        if let Some(status) = status_filter {
                            if batch.status != status {
                                return false;
                            }
                        }

                        // 关键词筛选
                        if let Some(keyword) = keyword {
                            let keyword_lower = keyword.to_lowercase();
                            if !batch.name.to_lowercase().contains(&keyword_lower)
                                && !batch.description.as_ref().unwrap_or(&String::new()).to_lowercase().contains(&keyword_lower) {
                                return false;
                            }
                        }

                        true
                    })
                    .cloned()
                    .collect();

                // 按创建时间降序排序
                filtered_batches.sort_by(|a, b| b.create_time.cmp(&a.create_time));

                let total = filtered_batches.len() as i32;
                let start = (page_no * page_size) as usize;
                let end = ((page_no + 1) * page_size) as usize;

                let page_batches = if start < filtered_batches.len() {
                    filtered_batches[start..end.min(filtered_batches.len())].to_vec()
                } else {
                    Vec::new()
                };

                (page_batches, total)
            }
        }
    }

    /// 删除批次
    pub async fn delete_batch(&self, batch_id: &str) -> bool {
        // 检查是否正在运行
        {
            let running_tasks = self.running_tasks.lock().unwrap();
            if running_tasks.get(batch_id).unwrap_or(&false) == &true {
                return false; // 正在运行的任务不能删除
            }
        }

        // 解析批次ID
        let batch_id_u64 = match batch_id.parse::<u64>() {
            Ok(id) => id,
            Err(_) => {
                eprintln!("无效的批次ID: {}", batch_id);
                return false;
            }
        };

        // 从数据库删除匹配结果
        if let Err(e) = SentenceMatchRepository::delete_by_batch_id(batch_id_u64).await {
            eprintln!("删除数据库匹配结果失败: {}", e);
        }

        // 从数据库删除批次
        let db_deleted = PlagiarismBatchRepository::delete_by_batch_id(batch_id_u64).await.unwrap_or_else(|e| {
            eprintln!("删除数据库批次失败: {}", e);
            false
        });

        // 从内存缓存删除
        let mut batches = self.batches.write().await;
        let mut matches = self.matches.write().await;
        let memory_deleted = batches.remove(batch_id).is_some() | matches.remove(batch_id).is_some();

        db_deleted || memory_deleted
    }

    /// 取消正在运行的任务
    pub async fn cancel_task(&self, batch_id: &str) -> bool {
        {
            let mut running_tasks = self.running_tasks.lock().unwrap();
            running_tasks.insert(batch_id.to_string(), false); // 标记为取消
        }
        
        self.update_batch_status(batch_id, "failed", None).await;
        true
    }

    /// 标记任务开始
    pub fn mark_task_started(&self, batch_id: &str) {
        let mut running_tasks = self.running_tasks.lock().unwrap();
        running_tasks.insert(batch_id.to_string(), true);
    }

    /// 标记任务完成
    pub fn mark_task_completed(&self, batch_id: &str) {
        let mut running_tasks = self.running_tasks.lock().unwrap();
        running_tasks.remove(batch_id);
    }

    /// 检查任务是否被取消
    pub fn is_task_cancelled(&self, batch_id: &str) -> bool {
        let running_tasks = self.running_tasks.lock().unwrap();
        running_tasks.get(batch_id).unwrap_or(&true) == &false
    }

    /// 获取批次统计信息
    pub async fn get_batch_statistics(&self, batch_id: &str) -> Option<BatchStatistics> {
        let batch = self.get_batch(batch_id).await?;

        // 解析批次ID
        let batch_id_u64 = match batch_id.parse::<u64>() {
            Ok(id) => id,
            Err(_) => {
                eprintln!("无效的批次ID: {}", batch_id);
                return None;
            }
        };

        // 从数据库获取统计信息
        match SentenceMatchRepository::get_batch_statistics(batch_id_u64).await {
            Ok(Some(mut stats)) => {
                stats.total_books = batch.book_ids.len() as i32;
                Some(stats)
            }
            Ok(None) => {
                // 如果数据库中没有匹配结果，返回空统计
                Some(BatchStatistics {
                    batch_id: batch_id.to_string(),
                    total_books: batch.book_ids.len() as i32,
                    total_sentences: 0,
                    total_matches: 0,
                    exact_matches: 0,
                    similar_matches: 0,
                    partial_matches: 0,
                    average_similarity: 0.0,
                    high_similarity_matches: 0,
                    medium_similarity_matches: 0,
                    low_similarity_matches: 0,
                })
            }
            Err(e) => {
                eprintln!("从数据库获取统计信息失败: {}", e);

                // 回退到内存缓存
                let matches = self.matches.read().await;
                let batch_matches = matches.get(batch_id)?;

                let total_matches = batch_matches.len() as i32;
                let exact_matches = batch_matches.iter().filter(|m| m.match_type == "exact").count() as i32;
                let similar_matches = batch_matches.iter().filter(|m| m.match_type == "similar").count() as i32;
                let partial_matches = batch_matches.iter().filter(|m| m.match_type == "partial").count() as i32;

                let average_similarity = if total_matches > 0 {
                    batch_matches.iter().map(|m| m.similarity).sum::<f64>() / total_matches as f64
                } else {
                    0.0
                };

                let high_similarity_matches = batch_matches.iter().filter(|m| m.similarity > 0.8).count() as i32;
                let medium_similarity_matches = batch_matches.iter().filter(|m| m.similarity >= 0.5 && m.similarity <= 0.8).count() as i32;
                let low_similarity_matches = batch_matches.iter().filter(|m| m.similarity < 0.5).count() as i32;

                Some(BatchStatistics {
                    batch_id: batch_id.to_string(),
                    total_books: batch.book_ids.len() as i32,
                    total_sentences: 0, // 这里需要根据实际情况计算
                    total_matches,
                    exact_matches,
                    similar_matches,
                    partial_matches,
                    average_similarity,
                    high_similarity_matches,
                    medium_similarity_matches,
                    low_similarity_matches,
                })
            }
        }
    }

    /// 生成批次ID
    fn generate_batch_id(&self) -> String {
        use crate::utils::snowflake_generator::SnowflakeGeneratorUtil;
        SnowflakeGeneratorUtil::next().to_string()
    }
}

impl Default for BatchManager {
    fn default() -> Self {
        Self::new()
    }
}

// 全局批次管理器实例
use lazy_static::lazy_static;

lazy_static! {
    pub static ref GLOBAL_BATCH_MANAGER: BatchManager = BatchManager::new();
}
